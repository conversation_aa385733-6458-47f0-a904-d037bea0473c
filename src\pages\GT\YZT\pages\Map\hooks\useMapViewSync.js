import { useEffect, useCallback, useRef } from 'react';
import { Cesium } from 'umi';
import mapViewSynchronizer from '../services/MapViewSynchronizer';

// 防抖函数，避免频繁触发视图同步
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 自定义 Hook，用于集成地图视图同步功能
 * 支持 2D (Leaflet) 和 3D (Cesium) 地图
 *
 * @param {Object} mapInstance - 地图实例（Leaflet 或 Cesium）
 * @param {string} mapId - 地图唯一标识
 * @param {boolean} syncEnabled - 是否启用同步
 * @param {string} mapType - 地图类型，'2D' 或 '3D'
 * @param {Object} options - 额外配置选项
 * @param {number} options.debounceTime - 防抖时间（毫秒）
 * @param {boolean} options.smoothAnimation - 是否使用平滑动画
 * @returns {Object} - 包含处理地图视图变化的方法和状态
 */
const useMapViewSync = (
  mapInstance,
  mapId,
  syncEnabled = false,
  mapType = '2D',
  options = { debounceTime: 300, smoothAnimation: true }
) => {
  // 保存上一次的视图状态，用于比较变化
  const lastViewStateRef = useRef(null);
  
  // 错误状态
  const errorRef = useRef(null);
  // 设置同步启用状态
  useEffect(() => {
    mapViewSynchronizer.setEnabled(syncEnabled);
  }, [syncEnabled]);

  // 处理来自其他地图的视图更新
  const handleExternalViewUpdate = useCallback((viewState) => {
    if (!mapInstance) return;
    
    // 根据地图类型选择不同的更新方法
    if (mapType === '2D') {
      // Leaflet 地图视图更新
      if (viewState.center && typeof mapInstance.setView === 'function') {
        mapInstance.setView(
          [viewState.center.lat, viewState.center.lng], 
          viewState.zoom,
          { animate: true, duration: 0.5 }
        );
      }
    } else if (mapType === '3D') {
      // Cesium 地图视图更新
      if (mapInstance.camera && typeof mapInstance.camera.flyTo === 'function') {
        mapInstance.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(
            viewState.center.lng,
            viewState.center.lat,
            viewState.height || 1000
          ),
          orientation: {
            heading: viewState.heading || 0,
            pitch: viewState.pitch || -Math.PI/2,
            roll: viewState.roll || 0
          },
          duration: 0.5
        });
      }
    }
  }, [mapInstance, mapType]);

  // 处理当前地图的视图变化（使用防抖）
  const handleViewChange = useCallback(
    debounce((viewState) => {
      if (!mapId || !syncEnabled) return;
      
      try {
        // 检查视图状态是否有效
        if (!viewState || !viewState.center) {
          console.warn(`地图 ${mapId} 的视图状态无效`, viewState);
          return;
        }
        
        // 检查视图状态是否与上一次相同
        const lastViewState = lastViewStateRef.current;
        if (lastViewState) {
          const isSameCenter =
            Math.abs(lastViewState.center.lat - viewState.center.lat) < 0.000001 &&
            Math.abs(lastViewState.center.lng - viewState.center.lng) < 0.000001;
            
          const isSameZoom = lastViewState.zoom === viewState.zoom;
          
          if (isSameCenter && isSameZoom) {
            // 视图没有实质性变化，跳过更新
            return;
          }
        }
        
        // 更新上一次视图状态
        lastViewStateRef.current = { ...viewState };
        
        // 通知其他地图
        mapViewSynchronizer.notifyViewChange(mapId, viewState);
      } catch (error) {
        console.error(`地图 ${mapId} 处理视图变化时出错:`, error);
        errorRef.current = error;
      }
    }, options.debounceTime),
    [mapId, syncEnabled, options.debounceTime]
  );

  // 获取当前地图视图状态
  const getCurrentViewState = useCallback(() => {
    if (!mapInstance) return null;
    console.log('getCurrentViewState', mapType, mapInstance);

    if (mapType === '2D') {
      // Leaflet 地图
      const center = mapInstance.getCenter();
      const zoom = mapInstance.getZoom();
      
      return {
        center: {
          lat: center.lat,
          lng: center.lng
        },
        zoom: zoom
      };
    } else if (mapType === '3D') {
      // Cesium 地图
      const camera = mapInstance.camera;
      if (!camera) return null;
      
      const position = camera.positionCartographic;
      
      return {
        center: {
          lat: Cesium.Math.toDegrees(position.latitude),
          lng: Cesium.Math.toDegrees(position.longitude)
        },
        height: position.height,
        heading: camera.heading,
        pitch: camera.pitch,
        roll: camera.roll
      };
    }
    
    return null;
  }, [mapInstance, mapType]);

  // 订阅和取消订阅同步服务
  useEffect(() => {
    if (!mapInstance || !mapId) return;

    // 订阅同步服务
    mapViewSynchronizer.subscribe(mapId, mapInstance, {
      onViewChange: handleExternalViewUpdate
    });

    console.log(`地图 ${mapId} 已订阅视图同步服务`);

    // 组件卸载时取消订阅
    return () => {
      mapViewSynchronizer.unsubscribe(mapId);
      console.log(`地图 ${mapId} 已取消订阅视图同步服务`);
    };
  }, [mapInstance, mapId, handleExternalViewUpdate]);

  // 设置地图移动事件监听
  useEffect(() => {
    if (!mapInstance || !mapId || !syncEnabled) return;

    let moveEndHandler;
    let moveHandler;
    
    try {
      if (mapType === '2D') {
        // Leaflet 地图移动事件
        moveEndHandler = () => {
          const viewState = getCurrentViewState();
          if (viewState) {
            handleViewChange(viewState);
          }
        };
        
        // 监听 moveend 事件（地图移动结束）
        mapInstance.on('moveend', moveEndHandler);
        
        // 可选：监听 move 事件（地图移动中），用于更实时的同步
        if (options.realtimeSync) {
          moveHandler = debounce(() => {
            const viewState = getCurrentViewState();
            if (viewState) {
              handleViewChange(viewState);
            }
          }, 100); // 使用更短的防抖时间
          
          mapInstance.on('move', moveHandler);
        }
      } else if (mapType === '3D') {
        // Cesium 地图移动事件
        moveEndHandler = () => {
          const viewState = getCurrentViewState();
          if (viewState) {
            handleViewChange(viewState);
          }
        };
        
        if (mapInstance.camera) {
          // 监听 moveEnd 事件（相机移动结束）
          mapInstance.camera.moveEnd.addEventListener(moveEndHandler);
          
          // 可选：监听 changed 事件（相机变化中），用于更实时的同步
          if (options.realtimeSync) {
            moveHandler = debounce(() => {
              const viewState = getCurrentViewState();
              if (viewState) {
                handleViewChange(viewState);
              }
            }, 100);
            
            mapInstance.camera.changed.addEventListener(moveHandler);
          }
        }
      }
    } catch (error) {
      console.error(`地图 ${mapId} 设置事件监听时出错:`, error);
      errorRef.current = error;
    }

    // 清理事件监听
    return () => {
      try {
        if (mapType === '2D') {
          mapInstance.off('moveend', moveEndHandler);
          if (options.realtimeSync) {
            mapInstance.off('move', moveHandler);
          }
        } else if (mapType === '3D' && mapInstance.camera) {
          mapInstance.camera.moveEnd.removeEventListener(moveEndHandler);
          if (options.realtimeSync) {
            mapInstance.camera.changed.removeEventListener(moveHandler);
          }
        }
      } catch (error) {
        console.error(`地图 ${mapId} 清理事件监听时出错:`, error);
      }
    };
  }, [mapInstance, mapId, syncEnabled, mapType, getCurrentViewState, handleViewChange, options.realtimeSync]);

  // 手动触发视图同步
  const syncViewNow = useCallback(() => {
    if (!mapInstance || !mapId || !syncEnabled) return false;
    
    try {
      const viewState = getCurrentViewState();
      if (viewState) {
        // 绕过防抖，立即同步
        mapViewSynchronizer.notifyViewChange(mapId, viewState);
        return true;
      }
    } catch (error) {
      console.error(`地图 ${mapId} 手动同步视图时出错:`, error);
      errorRef.current = error;
    }
    
    return false;
  }, [mapInstance, mapId, syncEnabled, getCurrentViewState]);
  
  // 重置错误状态
  const resetError = useCallback(() => {
    errorRef.current = null;
  }, []);

  return {
    // 核心方法
    handleViewChange,
    getCurrentViewState,
    syncViewNow,
    
    // 状态信息
    isSyncEnabled: syncEnabled,
    hasError: !!errorRef.current,
    error: errorRef.current,
    resetError,
    
    // 同步服务状态
    getSyncStatus: () => mapViewSynchronizer.isEnabled(),
    getObserverCount: () => mapViewSynchronizer.getObserverCount(),
    isSubscribed: () => mapViewSynchronizer.hasObserver(mapId)
  };
};

export default useMapViewSync;